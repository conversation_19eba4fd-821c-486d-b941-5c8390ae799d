<?php

require __DIR__.'/vendor/autoload.php';

$app = require_once __DIR__.'/bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
$kernel->bootstrap();

echo "=== ANALYSE COMPLÈTE DU PROJET DEVSAI ===\n\n";

// 1. Vérification des fichiers dupliqués
echo "1. 🔍 RECHERCHE DE FICHIERS DUPLIQUÉS\n";
$duplicateFiles = [];
$fileHashes = [];

function scanForDuplicates($dir, &$fileHashes, &$duplicateFiles) {
    if (!is_dir($dir)) return;

    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($dir));
    foreach ($iterator as $file) {
        if ($file->isFile() && in_array($file->getExtension(), ['php', 'blade.php', 'js', 'css'])) {
            $content = file_get_contents($file->getPathname());
            $hash = md5($content);

            if (isset($fileHashes[$hash])) {
                $duplicateFiles[] = [
                    'original' => $fileHashes[$hash],
                    'duplicate' => $file->getPathname(),
                    'size' => $file->getSize()
                ];
            } else {
                $fileHashes[$hash] = $file->getPathname();
            }
        }
    }
}

$dirsToScan = ['app/', 'resources/', 'config/', 'routes/'];
foreach ($dirsToScan as $dir) {
    if (is_dir($dir)) {
        scanForDuplicates($dir, $fileHashes, $duplicateFiles);
    }
}

if (empty($duplicateFiles)) {
    echo "   ✅ Aucun fichier dupliqué trouvé\n";
} else {
    echo "   ❌ Fichiers dupliqués trouvés:\n";
    foreach ($duplicateFiles as $dup) {
        echo "      - {$dup['duplicate']} (identique à {$dup['original']})\n";
    }
}

// 2. Vérification des routes dupliquées
echo "\n2. 🛣️ VÉRIFICATION DES ROUTES\n";
$routeFiles = ['routes/web.php', 'routes/api.php'];
$routes = [];
$duplicateRoutes = [];

foreach ($routeFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        preg_match_all('/Route::(get|post|put|delete|patch)\s*\(\s*[\'"]([^\'"]+)[\'"]/', $content, $matches);

        for ($i = 0; $i < count($matches[0]); $i++) {
            $method = $matches[1][$i];
            $path = $matches[2][$i];
            $routeKey = $method . ':' . $path;

            if (isset($routes[$routeKey])) {
                $duplicateRoutes[] = $routeKey . " dans $file";
            } else {
                $routes[$routeKey] = $file;
            }
        }
    }
}

if (empty($duplicateRoutes)) {
    echo "   ✅ Aucune route dupliquée trouvée\n";
} else {
    echo "   ❌ Routes dupliquées:\n";
    foreach ($duplicateRoutes as $route) {
        echo "      - $route\n";
    }
}

// 3. Vérification des méthodes dupliquées dans les contrôleurs
echo "\n3. 🎮 VÉRIFICATION DES CONTRÔLEURS\n";
$controllerDir = 'app/Http/Controllers/';
$duplicateMethods = [];

if (is_dir($controllerDir)) {
    $iterator = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($controllerDir));
    foreach ($iterator as $file) {
        if ($file->isFile() && $file->getExtension() === 'php') {
            $content = file_get_contents($file->getPathname());
            preg_match_all('/public\s+function\s+(\w+)\s*\(/', $content, $matches);

            $methods = array_count_values($matches[1]);
            foreach ($methods as $method => $count) {
                if ($count > 1) {
                    $duplicateMethods[] = $file->getPathname() . " - méthode '$method' ($count fois)";
                }
            }
        }
    }
}

if (empty($duplicateMethods)) {
    echo "   ✅ Aucune méthode dupliquée dans les contrôleurs\n";
} else {
    echo "   ❌ Méthodes dupliquées:\n";
    foreach ($duplicateMethods as $method) {
        echo "      - $method\n";
    }
}

// 4. Vérification des configurations conflictuelles
echo "\n4. ⚙️ VÉRIFICATION DES CONFIGURATIONS\n";
$configIssues = [];

// Vérifier .env vs config
$envFile = '.env';
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);

    // Vérifier les doublons dans .env
    $envLines = explode("\n", $envContent);
    $envVars = [];
    foreach ($envLines as $line) {
        if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
            $key = trim(explode('=', $line)[0]);
            if (isset($envVars[$key])) {
                $configIssues[] = "Variable .env dupliquée: $key";
            } else {
                $envVars[$key] = true;
            }
        }
    }
}

// Vérifier les configurations manquantes
$requiredConfigs = [
    'APP_KEY', 'DB_CONNECTION', 'DB_HOST', 'DB_DATABASE',
    'MAIL_HOST', 'OPENAI_API_KEY'
];

foreach ($requiredConfigs as $config) {
    if (empty(env($config))) {
        $configIssues[] = "Configuration manquante: $config";
    }
}

if (empty($configIssues)) {
    echo "   ✅ Configurations correctes\n";
} else {
    echo "   ❌ Problèmes de configuration:\n";
    foreach ($configIssues as $issue) {
        echo "      - $issue\n";
    }
}

// 5. Vérification des imports/use dupliqués
echo "\n5. 📦 VÉRIFICATION DES IMPORTS\n";
$importIssues = [];

function checkDuplicateImports($file) {
    $content = file_get_contents($file);
    preg_match_all('/^use\s+([^;]+);/m', $content, $matches);

    $imports = array_count_values($matches[1]);
    $duplicates = [];
    foreach ($imports as $import => $count) {
        if ($count > 1) {
            $duplicates[] = trim($import);
        }
    }
    return $duplicates;
}

$phpFiles = new RecursiveIteratorIterator(new RecursiveDirectoryIterator('app/'));
foreach ($phpFiles as $file) {
    if ($file->isFile() && $file->getExtension() === 'php') {
        $duplicates = checkDuplicateImports($file->getPathname());
        if (!empty($duplicates)) {
            $importIssues[] = $file->getPathname() . ": " . implode(', ', $duplicates);
        }
    }
}

if (empty($importIssues)) {
    echo "   ✅ Aucun import dupliqué\n";
} else {
    echo "   ❌ Imports dupliqués:\n";
    foreach ($importIssues as $issue) {
        echo "      - $issue\n";
    }
}

// 6. Vérification des fichiers inutiles
echo "\n6. 🗑️ FICHIERS POTENTIELLEMENT INUTILES\n";
$unusedFiles = [];

$potentiallyUnused = [
    'test-*.php',
    '*-test.php',
    '*.bak',
    '*.old',
    '*.tmp',
    'debug.php',
    'temp.php'
];

foreach ($potentiallyUnused as $pattern) {
    $files = glob($pattern);
    $unusedFiles = array_merge($unusedFiles, $files);
}

// Chercher dans les sous-dossiers
$dirs = ['app/', 'resources/', 'config/', 'database/'];
foreach ($dirs as $dir) {
    if (is_dir($dir)) {
        foreach ($potentiallyUnused as $pattern) {
            $files = glob($dir . '**/' . $pattern, GLOB_BRACE);
            $unusedFiles = array_merge($unusedFiles, $files);
        }
    }
}

if (empty($unusedFiles)) {
    echo "   ✅ Aucun fichier inutile détecté\n";
} else {
    echo "   ⚠️ Fichiers potentiellement inutiles:\n";
    foreach ($unusedFiles as $file) {
        echo "      - $file\n";
    }
}

// 7. Résumé
echo "\n=== RÉSUMÉ DE L'ANALYSE ===\n";
$totalIssues = count($duplicateFiles) + count($duplicateRoutes) + count($duplicateMethods) +
               count($configIssues) + count($importIssues) + count($unusedFiles);

if ($totalIssues === 0) {
    echo "🎉 EXCELLENT! Aucun problème majeur détecté.\n";
    echo "✅ Le projet est bien structuré et optimisé.\n";
} else {
    echo "⚠️ $totalIssues problème(s) détecté(s):\n";
    echo "   - Fichiers dupliqués: " . count($duplicateFiles) . "\n";
    echo "   - Routes dupliquées: " . count($duplicateRoutes) . "\n";
    echo "   - Méthodes dupliquées: " . count($duplicateMethods) . "\n";
    echo "   - Problèmes de config: " . count($configIssues) . "\n";
    echo "   - Imports dupliqués: " . count($importIssues) . "\n";
    echo "   - Fichiers inutiles: " . count($unusedFiles) . "\n";
}

echo "\n=== FIN DE L'ANALYSE ===\n";
